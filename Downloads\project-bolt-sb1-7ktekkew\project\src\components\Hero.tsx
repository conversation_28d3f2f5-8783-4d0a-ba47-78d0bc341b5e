import React, { useState, useEffect } from "react";
import { ArrowDown, TrendingUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// --- HERO COMPONENT ---
const Hero = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const slides = [{
        image: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
        title: "Defining Lifestyles",
        subtitle: "Shreyas Properties",
        description: "Invest in a premium gated development in Bangalore's fastest-growing destination, near the International Airport.",
        highlight: "Ready for Registration | RERA Approved",
    }, {
        image: "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
        title: "Invest Now",
        subtitle: "Near International Airport",
        description: "Strategic location with excellent connectivity via State Highway 35, just minutes away from Devanahalli.",
        highlight: "High Appreciation Potential",
    }, {
        image: "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
        title: "Ready for Registration",
        subtitle: "Residential Plots",
        description: "Premium gated community with a swimming pool, clubhouse, sports facilities, and an eco-friendly environment.",
        highlight: "RERA & DTCP Approved",
    }, ];

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % slides.length);
        }, 6000);
        return () => clearInterval(timer);
    }, [slides.length]);

    const scrollToNext = () => {
        document.getElementById("properties")?.scrollIntoView({
            behavior: "smooth"
        });
    };

    const scrollToWhyInvest = () => {
        document.getElementById("why-invest")?.scrollIntoView({
            behavior: "smooth"
        });
    };

    return (
        <section id="home" className="relative h-screen overflow-hidden font-sans mobile-safe">
            {/* Background Slider */}
            <div className="absolute inset-0">
                <AnimatePresence>
                    <motion.div key={currentSlide}
                        initial={{
                            scale: 1.1,
                            opacity: 0
                        }}
                        animate={{
                            scale: 1,
                            opacity: 1
                        }}
                        exit={{
                            opacity: 0
                        }}
                        transition={{
                            duration: 1.5,
                            ease: "easeInOut"
                        }}
                        className="absolute inset-0 bg-cover bg-center"
                        style={{
                            backgroundImage: `url(${slides[currentSlide].image})`
                        }}
                    />
                </AnimatePresence>

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/10" />
            </div>

            {/* Mobile-Optimized Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-3 xs:px-4 sm:px-6">
                <div className="w-full max-w-5xl mx-auto text-center">
                    <motion.div key={currentSlide + "-content"} // Re-trigger animation on slide change
                        initial={{
                            y: 50,
                            opacity: 0
                        }}
                        animate={{
                            y: 0,
                            opacity: 1
                        }}
                        transition={{
                            duration: 0.8,
                            delay: 0.2,
                            ease: "easeOut"
                        }}
                        className="space-y-4 xs:space-y-6 sm:space-y-8">

                        {/* Mobile-optimized subtitle */}
                        <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-accent font-semibold text-primary-300 tracking-wider uppercase drop-shadow-md">
                            {slides[currentSlide].subtitle}
                        </h2>

                        {/* Mobile-optimized main title */}
                        <h1 className="text-hero-mobile md:text-hero font-display font-bold text-white drop-shadow-2xl leading-tight">
                            {slides[currentSlide].title}
                        </h1>

                        {/* Mobile-optimized description */}
                        <p className="text-body xs:text-body-lg md:text-body-xl text-slate-200 max-w-4xl mx-auto drop-shadow-sm leading-relaxed px-2 xs:px-0">
                            {slides[currentSlide].description}
                        </p>
                        {/* Mobile-optimized highlight badge */}
                        {slides[currentSlide].highlight && (
                            <div className="glass-premium rounded-2xl px-4 xs:px-6 py-3 xs:py-4 inline-block mx-2 xs:mx-0">
                                <p className="font-accent font-semibold text-white text-sm xs:text-base">
                                    {slides[currentSlide].highlight}
                                </p>
                            </div>
                        )}

                        {/* Mobile-optimized CTA button */}
                        <div className="pt-4 xs:pt-6">
                            <motion.button
                                onClick={scrollToWhyInvest}
                                className="btn-luxury text-sm xs:text-base flex items-center space-x-2 xs:space-x-3 mx-auto min-h-[44px] px-6 xs:px-8"
                                whileHover={{
                                    scale: 1.05,
                                    y: -2
                                }}
                                whileTap={{
                                    scale: 0.95
                                }}
                            >
                                <TrendingUp className="h-4 w-4 xs:h-5 xs:w-5" />
                                <span className="font-accent font-semibold">Why Invest in Shreyas Sunrise?</span>
                            </motion.button>
                        </div>
                    </motion.div>
                </div>
            </div>

            {/* Slide Indicators */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
                {slides.map((_, index) => (
                    <motion.button key={index}
                        onClick={() => setCurrentSlide(index)}
                        className={`h-2 w-8 rounded-full transition-all duration-300 ${
                            index === currentSlide ? "bg-orange-500" : "bg-white/40"
                        }`}
                        whileHover={{
                            scale: 1.2
                        }}
                        whileTap={{
                            scale: 0.8
                        }}
                    />
                ))}
            </div>

            {/* Scroll Indicator */}
            <motion.button onClick={scrollToNext}
                className="absolute bottom-6 right-6 p-3 text-white/70 hover:text-white transition-colors duration-300"
                animate={{
                    y: [0, 10, 0]
                }}
                transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
                whileHover={{
                    scale: 1.1
                }}>
                <ArrowDown className="h-6 w-6" />
            </motion.button>
        </section>
    );
};

export default Hero;
