import React from "react";
import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  MapPin,
  Facebook,
  Linkedin,
  Youtube,
  ArrowUp,
} from "lucide-react";

// --- FOOTER COMPONENT ---
const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const socialLinks = [
    { icon: Facebook, href: "https://www.facebook.com/ShreyasProperties/", label: "Facebook" },
    { icon: Youtube, href: "https://www.youtube.com/watch?v=DaUaY7KRrFI&t=1s", label: "YouTube" },
    { icon: Linkedin, href: "https://www.linkedin.com/company/shreyassunrise/", label: "LinkedIn" },
  ];

  const quickLinks = [
    { name: "Home", href: "#home" },
    { name: "About", href: "#about" },
    { name: "Projects", href: "#properties" },
    { name: "Why Invest", href: "#why-invest" },
    { name: "Contact", href: "#contact" },
  ];

  const services = [
    "Site Visits",
    "Investment Consultation",
    "RERA Approved Projects",
    "DTCP Approved",
    "Premium Amenities",
  ];

  return (
    <footer className="bg-slate-900 text-slate-300 font-sans">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-bold text-white">
                Shreyas Properties
              </h3>
              <p className="text-orange-400 font-semibold">
                defining lifestyles
              </p>
            </div>
            <p className="text-slate-400 text-sm leading-relaxed">
              Defining luxury living through exceptional properties with world-class amenities and unparalleled service across India.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-orange-500 hover:text-white transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <social.icon className="h-5 w-5" />
                </motion.a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="text-slate-400 hover:text-orange-400 transition-colors duration-300"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white">Services</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index} className="text-slate-400">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white">Contact Info</h4>
            <div className="space-y-4 text-sm">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-orange-400 flex-shrink-0 mt-1" />
                <p className="text-slate-400">
                  7-8/1, 4th Main, 4th block, Kalyan Nagar, Bengaluru, Karnataka 560043
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-orange-400 flex-shrink-0 mt-1" />
                <div>
                  <a href="tel:+919036699799" className="text-slate-400 hover:text-orange-400 transition-colors duration-300 block">
                    +91-9036699799
                  </a>
                  <a href="tel:+918151884545" className="text-slate-400 hover:text-orange-400 transition-colors duration-300 block">
                    +91-8151884545
                  </a>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-orange-400 flex-shrink-0 mt-1" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-slate-400 hover:text-orange-400 transition-colors duration-300"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-slate-500 text-sm text-center md:text-left">
              © {new Date().getFullYear()} Shreyas Properties. All rights reserved.
            </p>
            <div className="flex space-x-4">
                <a href="#" className="text-slate-500 hover:text-orange-400 text-sm">Privacy Policy</a>
                <a href="#" className="text-slate-500 hover:text-orange-400 text-sm">Terms of Service</a>
            </div>
          </div>
        </div>
      </div>

    </footer>
  );
};

export default Footer;
