import { LucideIcon } from "lucide-react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Building,
  MapPin,
  Users,
  TrendingUp,
  Clock,
  Briefcase,
  IndianRupee,
  Rocket,
} from "lucide-react";

// Animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: { y: 0, opacity: 1 },
};


// Helper component for stat cards with mobile optimization
interface StatCardProps {
  icon: LucideIcon;
  value: string;
  label: string;
  description: string;
}

const StatCard = ({ icon: Icon, value, label, description }: StatCardProps) => (
  <motion.div
    variants={itemVariants}
    className="card-luxury text-center hover-lift min-h-[140px] xs:min-h-[160px] sm:min-h-[180px]"
    transition={{ duration: 0.6, ease: "easeOut" }}
    whileHover={{ scale: 1.05 }}
  >
    <div className="flex justify-center mb-3 sm:mb-4">
      <div className="bg-primary/10 p-2 xs:p-3 rounded-xl border border-primary/20">
        <Icon className="h-5 w-5 xs:h-6 xs:w-6 sm:h-7 sm:w-7 text-primary" />
      </div>
    </div>
    <p className="text-xl xs:text-2xl sm:text-3xl lg:text-4xl font-display font-bold text-charcoal-800 mb-1">{value}</p>
    <p className="text-xs xs:text-sm font-accent font-semibold text-charcoal-600 mb-1 sm:mb-2">{label}</p>
    <p className="text-xs text-charcoal-500 font-body leading-tight px-1">{description}</p>
  </motion.div>
);

// Helper component for employment hub cards with mobile optimization
interface HubCardProps {
  hub: {
    name: string;
    investment: string;
    distance: string;
    employees: string;
    type: string;
    description: string;
    icon: LucideIcon;
    color: string;
    status: string;
  };
}

const HubCard = ({ hub }: HubCardProps) => {
  const statusColors: Record<string, string> = {
    Operational: "bg-success/10 text-success border-success/20",
    Expanding: "bg-info/10 text-info border-info/20",
    "Under Development": "bg-warning/10 text-warning border-warning/20",
    Developing: "bg-primary/10 text-primary border-primary/20",
  };

  return (
    <motion.div
      variants={itemVariants}
      className="card-luxury flex flex-col hover-lift"
      transition={{ duration: 0.6, ease: "easeOut" }}
      whileHover={{ scale: 1.02 }}
    >
      {/* Mobile-optimized header */}
      <div className="flex flex-col xs:flex-row xs:items-start space-y-3 xs:space-y-0 xs:space-x-4 mb-4 sm:mb-6">
        <div className="flex items-center xs:items-start space-x-3 xs:space-x-0 xs:flex-col">
          <div className="p-2 xs:p-3 rounded-xl bg-charcoal/5 border border-charcoal/10">
            <hub.icon className="h-6 w-6 xs:h-7 xs:w-7 sm:h-8 sm:w-8 text-charcoal-600" />
          </div>
          <span
            className={`text-xs font-accent font-medium px-2 xs:px-3 py-1 rounded-full border xs:mt-2 ${
              statusColors[hub.status] || "bg-charcoal/10 text-charcoal-700 border-charcoal/20"
            }`}
          >
            {hub.status}
          </span>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg xs:text-xl font-display font-bold text-charcoal-800 mb-2 leading-tight">{hub.name}</h3>
          <p className="text-sm xs:text-base text-charcoal-600 font-body leading-relaxed">{hub.description}</p>
        </div>
      </div>

      {/* Mobile-optimized stats grid */}
      <div className="mt-auto pt-4 sm:pt-6 grid grid-cols-2 gap-3 xs:gap-4 sm:gap-6 border-t border-charcoal/10">
        <div className="flex items-center space-x-2 min-w-0">
          <IndianRupee className="w-4 h-4 xs:w-5 xs:h-5 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-xs text-charcoal-500 font-body">Investment</p>
            <p className="text-sm xs:text-base font-accent font-semibold text-charcoal-800 truncate">{hub.investment}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 min-w-0">
          <Clock className="w-4 h-4 xs:w-5 xs:h-5 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-xs text-charcoal-500 font-body">Distance</p>
            <p className="text-sm xs:text-base font-accent font-semibold text-charcoal-800 truncate">{hub.distance}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 min-w-0">
          <Users className="w-4 h-4 xs:w-5 xs:h-5 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-xs text-charcoal-500 font-body">Employment</p>
            <p className="text-sm xs:text-base font-accent font-semibold text-charcoal-800 truncate">{hub.employees}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 min-w-0">
          <Briefcase className="w-4 h-4 xs:w-5 xs:h-5 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <p className="text-xs text-charcoal-500 font-body">Sector</p>
            <p className="text-sm xs:text-base font-accent font-semibold text-charcoal-800 truncate">{hub.type}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Main Component
const EmploymentHub = () => {
  // Intersection observer for scroll animations
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Data for the component
  const employmentHubs = [
    {
      name: "Foxconn iPhone Campus",
      investment: "₹22,000 Cr",
      distance: "15 mins",
      employees: "50,000+",
      type: "Manufacturing",
      description: "World's largest iPhone facility outside China.",
      icon: Building,
      color: "bg-blue-500/20 text-blue-300",
      status: "Under Development",
    },
    {
      name: "SAP Labs New Campus",
      investment: "₹1,500 Cr",
      distance: "18 mins",
      employees: "15,000+",
      type: "IT Services",
      description: "Major software development and innovation center.",
      icon: Building,
      color: "bg-green-500/20 text-green-300",
      status: "Expanding",
    },
    {
      name: "Infosys Facility",
      investment: "₹700 Cr",
      distance: "20 mins",
      employees: "25,000+",
      type: "IT Services",
      description: "Large-scale IT services and development center.",
      icon: Building,
      color: "bg-purple-500/20 text-purple-300",
      status: "Operational",
    },
    {
      name: "Aerospace Park",
      investment: "₹5,000 Cr",
      distance: "12 mins",
      employees: "30,000+",
      type: "Aerospace",
      description: "India's largest aerospace & defense hub.",
      icon: Rocket,
      color: "bg-orange-500/20 text-orange-300",
      status: "Developing",
    },
  ];

  const investmentStats = [
    {
      icon: IndianRupee,
      value: "₹29,200+",
      label: "Crores Investment",
      description: "Total industrial investment in the region",
    },
    {
      icon: Users,
      value: "1,20,000+",
      label: "Employment",
      description: "Direct and indirect job creation",
    },
    {
      icon: TrendingUp,
      value: "25%",
      label: "Annual Growth",
      description: "Property appreciation rate",
    },
    {
      icon: Clock,
      value: "20 mins",
      label: "Average Commute",
      description: "To major employment centers",
    },
  ];

  return (
    <section
      id="employment"
      className="section-padding section-luxury relative overflow-hidden"
    >
      {/* Premium Background Elements */}
      <div className="absolute top-20 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl floating-slow" />
      <div className="absolute bottom-20 right-10 w-80 h-80 bg-secondary/5 rounded-full blur-3xl floating-delayed" />

      <div className="container-luxury relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12 xs:space-y-16"
        >
          {/* Enhanced Mobile-First Header */}
          <motion.div
            variants={itemVariants}
            className="text-center space-y-4 xs:space-y-6 sm:space-y-8"
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <span className="inline-block px-3 xs:px-4 py-2 bg-primary/10 backdrop-blur-sm border border-primary/20 rounded-full text-overline font-accent text-primary-600 tracking-widest uppercase mb-4">
              Investment Corridor
            </span>
            <h2 className="text-section-mobile md:text-section font-display font-bold text-gradient-luxury px-4 xs:px-0">
              Strategic Employment Corridor
            </h2>
            <div className="w-16 xs:w-20 sm:w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto rounded-full" />
            <p className="text-body xs:text-body-lg text-charcoal-600 max-w-4xl mx-auto leading-relaxed font-body px-4 xs:px-6 sm:px-0">
              Positioned at the epicenter of North Bengaluru's industrial transformation,
              Shreyas Sunrise benefits from{" "}
              <span className="font-accent font-bold text-primary-600">₹29,000+ Crores</span> in
              corporate investments, establishing this region as a premier employment
              and business destination.
            </p>
          </motion.div>

          {/* Mobile-Optimized Investment Statistics Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-6 px-2 xs:px-0">
            {investmentStats.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </div>

          {/* Mobile-Optimized Employment Hubs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 xs:gap-8 px-2 xs:px-0">
            {employmentHubs.map((hub, index) => (
              <HubCard key={index} hub={hub} />
            ))}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default EmploymentHub;
