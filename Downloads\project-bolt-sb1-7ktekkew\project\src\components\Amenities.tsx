import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Waves,
  Utensils,
  Du<PERSON><PERSON>,
  Car,
  Wifi,
  Shield,
  Zap,
  TreePine,
  Wine,
  Camera,
  Headphones,
  Thermometer,
} from "lucide-react";

// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// --- AMENITIES COMPONENT ---
const Amenities = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const amenities = [
    { icon: Waves, title: "Infinity Pool", description: "Spectacular infinity pool with panoramic views", color: "text-blue-500 bg-blue-100" },
    { icon: Utensils, title: "Gourmet Kitchen", description: "Professional-grade appliances and custom cabinetry", color: "text-orange-500 bg-orange-40" },
    { icon: Dumbbell, title: "Private Gym", description: "Fully equipped fitness center with modern equipment", color: "text-red-500 bg-red-100" },
    { icon: Car, title: "Secure Parking", description: "Ample covered parking with EV charging stations", color: "text-gray-500 bg-gray-100" },
    { icon: Wifi, title: "Smart Home", description: "Integrated automation and high-speed internet", color: "text-green-500 bg-green-100" },
    { icon: Shield, title: "24/7 Security", description: "Advanced monitoring and professional security staff", color: "text-purple-500 bg-purple-100" },
    { icon: Zap, title: "Power Backup", description: "100% power backup for all homes and common areas", color: "text-yellow-500 bg-yellow-100" },
    { icon: TreePine, title: "Landscaped Gardens", description: "Beautifully manicured gardens and walking trails", color: "text-emerald-500 bg-emerald-100" },
    { icon: Wine, title: "Clubhouse", description: "Elegant clubhouse with lounge and party hall", color: "text-pink-500 bg-pink-100" },
    { icon: Camera, title: "Media Room", description: "Home theater with premium sound system", color: "text-indigo-500 bg-indigo-100" },
    { icon: Headphones, title: "Music Studio", description: "Soundproofed recording and practice space", color: "text-teal-500 bg-teal-100" },
    { icon: Thermometer, title: "Spa & Wellness", description: "Private spa with sauna and massage room", color: "text-cyan-500 bg-cyan-100" },
  ];

  return (
    <section id="amenities" className="py-16 sm:py-10 bg-orange-50 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600 mb-4">
              Luxury Amenities
            </h2>
            <div className="w-20 h-1 bg-orange-500 mx-auto mb-6" />
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Every detail has been carefully considered to provide an unparalleled lifestyle experience with world-class amenities and services.
            </p>
          </motion.div>

          {/* Amenities Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {amenities.map((amenity, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group relative"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col items-center text-center">
                  <div
                    className={`w-16 h-16 rounded-full ${amenity.color} flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}
                  >
                    <amenity.icon className="h-8 w-8" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-bold text-slate-800 group-hover:text-orange-600 transition-colors duration-300">
                      {amenity.title}
                    </h3>
                    <p className="text-sm text-slate-600">
                      {amenity.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Amenities;
