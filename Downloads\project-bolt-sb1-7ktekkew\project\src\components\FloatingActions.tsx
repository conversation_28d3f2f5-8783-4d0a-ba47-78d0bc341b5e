import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Phone, MessageCircle, Download, Calendar, X, Plus, ChevronUp } from "lucide-react";

const FloatingActions = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const actions = [
    {
      icon: Phone,
      label: "Call Now",
      href: "tel:+919036699799",
      color: "bg-green-500 hover:bg-green-600",
      action: () => window.open("tel:+919036699799")
    },
    {
      icon: MessageCircle,
      label: "WhatsApp",
      href: "https://api.whatsapp.com/send?phone=919035055655&text=I am interested with shreyas properties",
      color: "bg-green-600 hover:bg-green-700",
      action: () => window.open("https://api.whatsapp.com/send?phone=919035055655&text=I am interested with shreyas properties", "_blank")
    },
    {
      icon: Download,
      label: "Download Brochure",
      href: "http://www.shreyasproperties.com/broucher.pdf",
      color: "bg-primary hover:bg-primary-700",
      action: () => window.open("http://www.shreyasproperties.com/broucher.pdf", "_blank")
    },
    {
      icon: Calendar,
      label: "Book Site Visit",
      href: "#contact",
      color: "bg-secondary hover:bg-secondary-700",
      action: () => {
        const element = document.querySelector("#contact");
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      }
    }
  ];

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleActionClick = (action: typeof actions[0]) => {
    action.action();
    setIsOpen(false);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <>
      {/* Scroll to Top Button */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            onClick={scrollToTop}
            className="fixed bottom-24 right-6 bg-accent-500 hover:bg-accent-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 z-40"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            title="Scroll to top"
          >
            <ChevronUp size={20} />
          </motion.button>
        )}
      </AnimatePresence>

      {/* Main Floating Actions */}
      <div className="fixed bottom-6 right-6 z-50">
      {/* Action Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="flex flex-col space-y-3 mb-4"
          >
            {actions.map((action, index) => (
              <motion.button
                key={action.label}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleActionClick(action)}
                className={`${action.color} text-white p-3 rounded-full shadow-lg transition-all duration-300 group flex items-center space-x-3 pr-4`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <action.icon size={20} />
                <span className="text-sm font-medium whitespace-nowrap">
                  {action.label}
                </span>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Toggle Button */}
      <motion.button
        onClick={toggleMenu}
        className={`${
          isOpen 
            ? "bg-red-500 hover:bg-red-600" 
            : "bg-primary hover:bg-primary-700"
        } text-white p-4 rounded-full shadow-lg transition-all duration-300`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
      >
        {isOpen ? <X size={24} /> : <Plus size={24} />}
      </motion.button>
      </div>
    </>
  );
};

export default FloatingActions;
