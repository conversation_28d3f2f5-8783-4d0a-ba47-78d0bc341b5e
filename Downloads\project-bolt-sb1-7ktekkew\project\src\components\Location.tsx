import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import GoogleMap from "./GoogleMap";
import {
    MapPin,
    ExternalLink,
    Navigation,
    Briefcase,
    School,
    Landmark,
    Route,
    Plane,
    Clock,
    Car,
    Building,
    Layout
} from "lucide-react";
import layoutPlanImage from "../assets/layoutplan700.jpg";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};
const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};


const Location = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeTab, setActiveTab] = useState("amenities");

  const nearbyAmenities = [
    { icon: Plane, name: "Kempegowda Int'l Airport", distance: "20 minutes", type: "Airport", description: "Major international airport connectivity", color: "text-blue-500 bg-blue-100" },
    { icon: Route, name: "NH-44 Highway", distance: "5 minutes", type: "Highway", description: "Major highway connectivity to Bengaluru", color: "text-green-500 bg-green-100" },
    { icon: School, name: "Harrow Int'l School", distance: "10 minutes", type: "Education", description: "Premium international school", color: "text-purple-500 bg-purple-100" },
    { icon: Building, name: "Foxconn iPhone Campus", distance: "15 minutes", type: "Employment", description: "₹22,000 Crore manufacturing facility", color: "text-indigo-500 bg-indigo-100" },
    { icon: Landmark, name: "Nandi Hills", distance: "15 minutes", type: "Recreation", description: "Popular hill station and tourist spot", color: "text-teal-500 bg-teal-100" },
    { icon: School, name: "Amity University", distance: "12 minutes", type: "Education", description: "Leading private university", color: "text-orange-500 bg-orange-100" },
    { icon: Building, name: "SAP Labs Campus", distance: "18 minutes", type: "Employment", description: "Major IT development center", color: "text-sky-500 bg-sky-100" },
    { icon: School, name: "GITAM University", distance: "15 minutes", type: "Education", description: "Renowned educational institution", color: "text-pink-500 bg-pink-100" },
  ];

  const transportation = [
    { icon: Car, name: "Hebbal", time: "25 minutes", method: "Drive" },
    { icon: Plane, name: "Kempegowda Airport", time: "20 minutes", method: "Drive" },
    { icon: Car, name: "Yelahanka", time: "20 minutes", method: "Drive" },
    { icon: Navigation, name: "Metro Phase 2 (Upcoming)", time: "8 minutes", method: "Metro" },
  ];

  const neighborhoods = [
    { name: "Devanahalli", image: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800", description: "North Bengaluru's fastest-growing investment hotspot.", highlights: ["Airport Proximity", "Industrial Growth", "Educational Hub"] },
    { name: "Hebbal", image: "https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Established locality with excellent infrastructure.", highlights: ["IT Corridor", "Shopping Centers", "Healthcare"] },
    { name: "Yelahanka", image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Well-developed area with good social infrastructure.", highlights: ["Education", "Parks", "Commercial Centers"] },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "amenities":
        return (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {nearbyAmenities.map((amenity, index) => (
              <motion.div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group" variants={itemVariants}>
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 rounded-full ${amenity.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <amenity.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-bold text-slate-800">{amenity.name}</h4>
                      <span className="text-orange-600 text-sm font-bold">{amenity.distance}</span>
                    </div>
                    <p className="text-xs text-slate-500 font-semibold mb-2">{amenity.type}</p>
                    <p className="text-slate-600 text-sm">{amenity.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      case "transport":
        return (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {transportation.map((transport, index) => (
              <motion.div key={index} className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300" variants={itemVariants} whileHover={{ y: -5, scale: 1.02 }}>
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <transport.icon className="h-8 w-8 text-orange-600" />
                </div>
                <h4 className="font-bold text-slate-800 text-lg mb-1">{transport.name}</h4>
                <div className="flex items-center justify-center space-x-2 text-slate-600">
                  <Clock className="h-4 w-4" />
                  <span className="font-semibold">{transport.time}</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">by {transport.method}</p>
              </motion.div>
            ))}
          </motion.div>
        );
      case "neighborhoods":
        return (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {neighborhoods.map((neighborhood, index) => (
              <motion.div key={index} className="bg-white rounded-2xl overflow-hidden shadow-xl group" variants={itemVariants} whileHover={{ y: -8 }}>
                <div className="relative h-48 overflow-hidden">
                  <img src={neighborhood.image} alt={neighborhood.name} className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500" loading="lazy" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4">
                    <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                  </div>
                </div>
                <div className="p-6 space-y-4">
                  <p className="text-slate-600">{neighborhood.description}</p>
                  <div>
                    <h4 className="font-semibold text-slate-700 text-sm mb-2">Key Highlights:</h4>
                    <div className="flex flex-wrap gap-2">
                      {neighborhood.highlights.map((highlight, idx) => (
                        <span key={idx} className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-semibold">{highlight}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      case "layout":
        return (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="flex justify-center">
            <div className="bg-white rounded-2xl overflow-hidden shadow-xl max-w-4xl w-full">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                    <Layout className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-slate-800">Project Layout Plan</h3>
                    <p className="text-slate-600 text-sm">Comprehensive development layout and plot distribution</p>
                  </div>
                </div>
              </div>
              <div className="relative">
                <img
                  src={layoutPlanImage}
                  alt="Shreyas Infra Projects Layout Plan - Plot Distribution and Development Layout"
                  className="w-full h-auto object-contain hover:scale-105 transition-transform duration-500"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
              </div>
              <div className="p-6 bg-orange-50">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-orange-600 mb-1">30 Acres</div>
                    <div className="text-slate-600 text-sm">Total Development Area</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-600 mb-1">Multiple Sizes</div>
                    <div className="text-slate-600 text-sm">Plot Configurations Available</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-600 mb-1">Premium</div>
                    <div className="text-slate-600 text-sm">Gated Community</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <section id="location-details" className="py-16 sm:py-10 bg-orange-100 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div variants={itemVariants}>
            <GoogleMap />
          </motion.div>
          
          <motion.div variants={itemVariants} className="flex justify-center">
            <div className="flex space-x-1 bg-white rounded-full p-2 shadow-md">
              {[
                { id: "amenities", label: "Nearby Amenities" },
                { id: "transport", label: "Transportation" },
                { id: "neighborhoods", label: "Neighborhoods" },
                { id: "layout", label: "Layout Plan" },
              ].map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full font-semibold transition-all duration-300 text-sm sm:text-base ${
                    activeTab === tab.id ? "bg-orange-500 text-white shadow-lg" : "text-slate-600 hover:bg-orange-100"
                  }`}
                  whileHover={{ scale: activeTab === tab.id ? 1 : 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tab.label}
                </motion.button>
              ))}
            </div>
          </motion.div>
          
          <div className="min-h-[300px]">
            {renderContent()}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default Location;